#!/bin/bash

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <dpClusterId-dpId>"
    echo "示例: $0 11-2059"
    exit 1
fi

# 解析参数
PARAM=$1

# 检查参数格式是否正确
if [[ ! $PARAM =~ ^[0-9]+-[0-9]+$ ]]; then
    echo "错误: 参数格式不正确,请使用 dpClusterId-dpId 格式"
    echo "示例: $0 11-2059"
    exit 1
fi

# 分割参数
dpClusterId=${PARAM%%-*}  # 获取横杠前的部分
dpId=${PARAM##*-}        # 获取横杠后的部分

# 发送企业微信通知函数
send_weixin_notice() {
    local content="$1"
    echo "发送企业微信通知..."
    
    curl -X POST --silent \
    --header 'Content-Type: application/json' \
    --header 'Accept: application/json' \
    -d '{  
        "content": "'"$content"'", 
        "messageType": "markdown", 
        "robotKey": "a93a7782-1bd0-4659-836d-a64b0b7f32bc" 
    }' 'http://10.30.50.177:6689/ct-xyl/fhxyy/wxserver/weixin/robot/msg/send'
    
    echo "企业微信通知已发送"
}

echo "开始调用DP调度API: dpClusterId=${dpClusterId}, dpId=${dpId}"

# 调用API接口并保存响应
response=$(curl --silent --location 'http://10.30.50.124:8080/dp/dispatch' \
--header 'Content-Type: application/json' \
--data '{
    "dpClusterId": '$dpClusterId',
    "dpId": '$dpId'
}')

# 检查curl是否成功执行
if [ $? -ne 0 ]; then
    error_msg="DP调度API调用失败: 网络错误或服务不可用 (dpClusterId=${dpClusterId}, dpId=${dpId})"
    echo "$error_msg"
    send_weixin_notice "$error_msg"
    exit 1
fi

# 检查响应是否为空
if [ -z "$response" ]; then
    error_msg="DP调度API调用失败: 收到空响应 (dpClusterId=${dpClusterId}, dpId=${dpId})"
    echo "$error_msg"
    send_weixin_notice "$error_msg"
    exit 1
fi

echo "接口响应: $response"

# 从响应中提取code字段
code=$(echo "$response" | grep -o '"code":[0-9]*' | cut -d':' -f2)

# 检查是否成功提取code
if [ -z "$code" ]; then
    error_msg="DP调度API调用失败: 无法从响应中提取code (dpClusterId=${dpClusterId}, dpId=${dpId})"
    echo "$error_msg"
    send_weixin_notice "$error_msg"
    exit 1
fi

# 检查code是否为200
if [ "$code" = "200" ]; then
    echo "任务调度成功"
    exit 0
else
    error_msg="DP调度API调用失败: 返回码=${code} (dpClusterId=${dpClusterId}, dpId=${dpId})"
    echo "任务调度失败,返回码: $code"
    exit 1
fi 