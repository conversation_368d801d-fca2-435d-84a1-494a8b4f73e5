#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经典背离趋势分析器
专注于RSI、MACD、KDJ背离信号的识别和分析

版本: 3.0 Divergence Focus
更新日期: 2025-07-11
"""

import pandas as pd
import numpy as np
import tushare as ts
from datetime import datetime, timedelta
from pathlib import Path
import configparser
import sqlite3
import logging
from scipy.signal import argrelextrema
import warnings

warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('divergence_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SimpleDataManager:
    """简化的数据管理器"""
    
    def __init__(self, cache_dir=None):
        self.cache_dir = Path(cache_dir) if cache_dir else Path(__file__).parent / "cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "simple_cache.db"
        self.api_key = self._load_api_key()
        self._init_db()
        
    def _load_api_key(self):
        """加载API密钥"""
        config_path = Path(__file__).parent / "config.ini"
        if config_path.exists():
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            return config.get("API", "tushare_token", fallback="5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77")
        return "5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77"
    
    def _init_db(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fund_data (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL,
                    PRIMARY KEY (ts_code, trade_date)
                )
            """)
            conn.commit()
            conn.close()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def get_fund_data(self, ts_code, days=500):
        """获取基金数据"""
        try:
            # 检查缓存
            conn = sqlite3.connect(self.db_path)
            df_cache = pd.read_sql_query(
                "SELECT * FROM fund_data WHERE ts_code=? ORDER BY trade_date DESC LIMIT ?",
                conn, params=(ts_code, days)
            )
            conn.close()
            
            # 如果缓存数据足够且较新，直接使用
            if len(df_cache) >= min(days, 100):
                latest_date = pd.to_datetime(df_cache['trade_date'].max())
                if (datetime.now() - latest_date).days <= 1:
                    logger.info(f"使用缓存数据: {ts_code}")
                    return df_cache.sort_values('trade_date').reset_index(drop=True)
            
            # 获取新数据
            logger.info(f"获取新数据: {ts_code}")
            ts.set_token(self.api_key)
            pro = ts.pro_api()
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days*2)).strftime('%Y%m%d')
            
            df = pro.fund_daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            if df.empty:
                logger.warning(f"未获取到数据: {ts_code}")
                return pd.DataFrame()
            
            # 数据处理
            df = df.sort_values('trade_date').reset_index(drop=True)
            df['volume'] = df.get('vol', 0)
            
            # 保存到缓存
            conn = sqlite3.connect(self.db_path)
            df.to_sql('fund_data', conn, if_exists='replace', index=False)
            conn.close()
            
            return df.tail(days).reset_index(drop=True)
            
        except Exception as e:
            logger.error(f"获取数据失败 {ts_code}: {e}")
            return pd.DataFrame()
    
    def get_fund_list(self):
        """获取常用基金列表"""
        return {
            "501312.SH": "海外科技LOF",
            "513260.SH": "恒生科技ETF",
            "513060.SH": "恒生医疗ETF", 
            "159302.SZ": "港股高股息ETF",
            "512890.SH": "红利低波ETF",
            "515100.SH": "红利低波100ETF",
            "160723.SZ": "嘉实原油LOF",
            "159509.SZ": "纳指科技ETF",
            "515300.SH": "日经225ETF",
            "515050.SH": "5G通信ETF",
            "516160.SH": "新能源ETF",
            "159928.SZ": "消费ETF",
            "512000.SH": "券商ETF",
            "159985.SZ": "豆粕ETF",
            "513630.SH": "港股红利ETF",
            "517900.SH": "银行ETF优选"
        }


class TechnicalIndicators:
    """技术指标计算"""
    
    @staticmethod
    def rsi(prices, period=14):
        """计算RSI指标"""
        delta = np.diff(prices)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)
        
        avg_gain = pd.Series(gain).rolling(window=period).mean()
        avg_loss = pd.Series(loss).rolling(window=period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return np.concatenate([[np.nan], rsi.values])
    
    @staticmethod
    def macd(prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        exp1 = pd.Series(prices).ewm(span=fast).mean()
        exp2 = pd.Series(prices).ewm(span=slow).mean()
        
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return macd_line.values, signal_line.values, histogram.values
    
    @staticmethod
    def kdj(high, low, close, period=9):
        """计算KDJ指标"""
        lowest_low = pd.Series(low).rolling(window=period).min()
        highest_high = pd.Series(high).rolling(window=period).max()
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        k = rsv.ewm(alpha=1/3).mean()
        d = k.ewm(alpha=1/3).mean()
        j = 3 * k - 2 * d
        
        return k.values, d.values, j.values
    
    @staticmethod
    def detect_golden_death_cross(line1, line2, min_gap=0.001):
        """检测金叉死叉信号
        
        Args:
            line1: 快线数据 (如MACD线、KDJ的K线)
            line2: 慢线数据 (如Signal线、KDJ的D线)
            min_gap: 最小差值，避免噪音信号
            
        Returns:
            golden_crosses: 金叉点列表 [(index, value1, value2), ...]
            death_crosses: 死叉点列表 [(index, value1, value2), ...]
        """
        golden_crosses = []
        death_crosses = []
        
        if len(line1) < 2 or len(line2) < 2:
            return golden_crosses, death_crosses
        
        for i in range(1, len(line1)):
            # 跳过NaN值
            if (np.isnan(line1[i]) or np.isnan(line2[i]) or 
                np.isnan(line1[i-1]) or np.isnan(line2[i-1])):
                continue
            
            prev_diff = line1[i-1] - line2[i-1]
            curr_diff = line1[i] - line2[i]
            
            # 金叉：快线从下方穿越慢线向上
            if prev_diff <= -min_gap and curr_diff >= min_gap:
                golden_crosses.append((i, line1[i], line2[i]))
            
            # 死叉：快线从上方穿越慢线向下  
            elif prev_diff >= min_gap and curr_diff <= -min_gap:
                death_crosses.append((i, line1[i], line2[i]))
        
        return golden_crosses, death_crosses
    
    @staticmethod
    def detect_macd_signals(macd_line, signal_line):
        """检测MACD金叉死叉信号"""
        return TechnicalIndicators.detect_golden_death_cross(
            macd_line, signal_line, min_gap=0.001
        )
    
    @staticmethod  
    def detect_kdj_signals(k_line, d_line):
        """检测KDJ金叉死叉信号"""
        return TechnicalIndicators.detect_golden_death_cross(
            k_line, d_line, min_gap=1.0  # KDJ使用更大的间隔避免噪音
        )


class DivergenceDetector:
    """背离检测器 - 核心算法"""
    
    def __init__(self, lookback=20, min_distance=10):
        self.lookback = lookback  # 寻找极值点的回望期
        self.min_distance = min_distance  # 极值点之间的最小距离
    
    def find_peaks_troughs(self, data, order=5):
        """寻找波峰和波谷"""
        # 使用scipy寻找极值点
        peaks = argrelextrema(data, np.greater, order=order)[0]
        troughs = argrelextrema(data, np.less, order=order)[0]
        
        # 过滤掉太近的点
        peaks = self._filter_close_points(peaks, data, True)
        troughs = self._filter_close_points(troughs, data, False)
        
        return peaks, troughs
    
    def _filter_close_points(self, points, data, is_peak):
        """过滤掉距离太近的极值点"""
        if len(points) < 2:
            return points
        
        filtered = [points[0]]
        for i in range(1, len(points)):
            if points[i] - filtered[-1] >= self.min_distance:
                # 如果距离足够，比较值的大小
                if is_peak:
                    if data[points[i]] > data[filtered[-1]]:
                        filtered[-1] = points[i]  # 替换为更高的峰
                    else:
                        filtered.append(points[i])
                else:
                    if data[points[i]] < data[filtered[-1]]:
                        filtered[-1] = points[i]  # 替换为更低的谷
                    else:
                        filtered.append(points[i])
        
        return np.array(filtered)
    
    def detect_divergence(self, price_data, indicator_data, indicator_name):
        """检测背离信号"""
        # 寻找价格和指标的极值点
        price_peaks, price_troughs = self.find_peaks_troughs(price_data)
        ind_peaks, ind_troughs = self.find_peaks_troughs(indicator_data)
        
        bullish_divergences = []  # 底背离（看涨）
        bearish_divergences = []  # 顶背离（看跌）
        
        # 检测底背离（价格创新低，指标不创新低）
        for i in range(len(price_troughs)-1):
            for j in range(i+1, len(price_troughs)):
                p1, p2 = price_troughs[i], price_troughs[j]
                
                # 寻找对应的指标谷值
                ind_t1 = self._find_closest_point(ind_troughs, p1)
                ind_t2 = self._find_closest_point(ind_troughs, p2)
                
                if ind_t1 is not None and ind_t2 is not None:
                    # 价格创新低，但指标没有创新低
                    if (price_data[p2] < price_data[p1] and 
                        indicator_data[ind_t2] > indicator_data[ind_t1]):
                        
                        bullish_divergences.append({
                            'type': 'bullish',
                            'price_points': (p1, p2),
                            'indicator_points': (ind_t1, ind_t2),
                            'price_values': (price_data[p1], price_data[p2]),
                            'indicator_values': (indicator_data[ind_t1], indicator_data[ind_t2]),
                            'strength': self._calculate_divergence_strength(
                                price_data[p1], price_data[p2], 
                                indicator_data[ind_t1], indicator_data[ind_t2], False
                            ),
                            'indicator': indicator_name
                        })
        
        # 检测顶背离（价格创新高，指标不创新高）
        for i in range(len(price_peaks)-1):
            for j in range(i+1, len(price_peaks)):
                p1, p2 = price_peaks[i], price_peaks[j]
                
                # 寻找对应的指标峰值
                ind_p1 = self._find_closest_point(ind_peaks, p1)
                ind_p2 = self._find_closest_point(ind_peaks, p2)
                
                if ind_p1 is not None and ind_p2 is not None:
                    # 价格创新高，但指标没有创新高
                    if (price_data[p2] > price_data[p1] and 
                        indicator_data[ind_p2] < indicator_data[ind_p1]):
                        
                        bearish_divergences.append({
                            'type': 'bearish',
                            'price_points': (p1, p2),
                            'indicator_points': (ind_p1, ind_p2),
                            'price_values': (price_data[p1], price_data[p2]),
                            'indicator_values': (indicator_data[ind_p1], indicator_data[ind_p2]),
                            'strength': self._calculate_divergence_strength(
                                price_data[p1], price_data[p2], 
                                indicator_data[ind_p1], indicator_data[ind_p2], True
                            ),
                            'indicator': indicator_name
                        })
        
        return bullish_divergences, bearish_divergences
    
    def _find_closest_point(self, points, target, max_distance=10):
        """寻找最接近目标位置的点"""
        if len(points) == 0:
            return None
        
        distances = np.abs(points - target)
        min_idx = np.argmin(distances)
        
        if distances[min_idx] <= max_distance:
            return points[min_idx]
        return None
    
    def _calculate_divergence_strength(self, p1, p2, i1, i2, is_bearish):
        """计算背离强度"""
        price_change = abs((p2 - p1) / p1)
        indicator_change = abs((i2 - i1) / abs(i1)) if i1 != 0 else 0
        
        # 背离强度 = 价格变化幅度 / 指标变化幅度
        if indicator_change == 0:
            return 1.0
        
        strength = price_change / indicator_change
        return min(strength, 3.0)  # 限制最大强度


class DivergenceAnalyzer:
    """背离分析主类"""
    
    def __init__(self, cache_dir=None):
        self.data_manager = SimpleDataManager(cache_dir)
        self.detector = DivergenceDetector()
        self.indicators = TechnicalIndicators()
    
    def analyze_fund(self, ts_code, days=200):
        """分析基金的背离信号"""
        # 获取数据
        df = self.data_manager.get_fund_data(ts_code, days)
        if df.empty:
            return None
        
        # 准备数据
        prices = df['close'].values
        highs = df['high'].values
        lows = df['low'].values
        dates = pd.to_datetime(df['trade_date'])
        
        # 计算技术指标
        rsi = self.indicators.rsi(prices)
        macd_line, macd_signal, macd_hist = self.indicators.macd(prices)
        k, d, j = self.indicators.kdj(highs, lows, prices)
        
        # 检测背离
        all_divergences = []
        
        # RSI背离
        if not np.all(np.isnan(rsi)):
            bull_rsi, bear_rsi = self.detector.detect_divergence(prices, rsi, 'RSI')
            all_divergences.extend(bull_rsi + bear_rsi)
        
        # MACD背离
        if not np.all(np.isnan(macd_line)):
            bull_macd, bear_macd = self.detector.detect_divergence(prices, macd_line, 'MACD')
            all_divergences.extend(bull_macd + bear_macd)
        
        # KDJ背离（使用J线）
        if not np.all(np.isnan(j)):
            bull_kdj, bear_kdj = self.detector.detect_divergence(prices, j, 'KDJ')
            all_divergences.extend(bull_kdj + bear_kdj)
        
        # 检测金叉死叉信号
        golden_death_signals = []
        
        # MACD金叉死叉
        if not np.all(np.isnan(macd_line)):
            macd_golden, macd_death = self.indicators.detect_macd_signals(macd_line, macd_signal)
            for idx, val1, val2 in macd_golden:
                golden_death_signals.append({
                    'type': 'golden_cross',
                    'indicator': 'MACD',
                    'index': idx,
                    'date': dates.iloc[idx],
                    'fast_value': val1,
                    'slow_value': val2,
                    'description': 'MACD金叉'
                })
            for idx, val1, val2 in macd_death:
                golden_death_signals.append({
                    'type': 'death_cross',
                    'indicator': 'MACD', 
                    'index': idx,
                    'date': dates.iloc[idx],
                    'fast_value': val1,
                    'slow_value': val2,
                    'description': 'MACD死叉'
                })
        
        # KDJ金叉死叉
        if not np.all(np.isnan(k)):
            kdj_golden, kdj_death = self.indicators.detect_kdj_signals(k, d)
            for idx, val1, val2 in kdj_golden:
                golden_death_signals.append({
                    'type': 'golden_cross',
                    'indicator': 'KDJ',
                    'index': idx,
                    'date': dates.iloc[idx],
                    'fast_value': val1,
                    'slow_value': val2,
                    'description': 'KDJ金叉'
                })
            for idx, val1, val2 in kdj_death:
                golden_death_signals.append({
                    'type': 'death_cross',
                    'indicator': 'KDJ',
                    'index': idx,
                    'date': dates.iloc[idx],
                    'fast_value': val1,
                    'slow_value': val2,
                    'description': 'KDJ死叉'
                })
        
        # 按时间排序金叉死叉信号
        golden_death_signals.sort(key=lambda x: x['date'], reverse=True)
        
        # 按时间排序
        for div in all_divergences:
            div['date1'] = dates.iloc[div['price_points'][0]]
            div['date2'] = dates.iloc[div['price_points'][1]]
        
        all_divergences.sort(key=lambda x: x['date2'], reverse=True)
        
        return {
            'ts_code': ts_code,
            'data': df,
            'dates': dates,
            'prices': prices,
            'indicators': {
                'rsi': rsi,
                'macd_line': macd_line,
                'macd_signal': macd_signal,
                'macd_hist': macd_hist,
                'kdj_k': k,
                'kdj_d': d,
                'kdj_j': j
            },
            'divergences': all_divergences,
            'golden_death_signals': golden_death_signals
        }
    
    def get_latest_signals(self, ts_code, days=50):
        """获取最新的背离信号"""
        result = self.analyze_fund(ts_code, days)
        if not result:
            return []
        
        # 只返回最近的背离信号
        recent_divergences = []
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for div in result['divergences']:
            if div['date2'] >= cutoff_date:
                recent_divergences.append(div)
        
        return recent_divergences
    
    def generate_summary(self, ts_code):
        """生成背离分析摘要"""
        signals = self.get_latest_signals(ts_code)
        
        if not signals:
            return "未检测到近期背离信号"
        
        summary = []
        for signal in signals[:3]:  # 最多显示3个最新信号
            signal_type = "底背离(看涨)" if signal['type'] == 'bullish' else "顶背离(看跌)"
            strength = "强" if signal['strength'] > 1.5 else "中" if signal['strength'] > 1.0 else "弱"
            date_str = signal['date2'].strftime('%m-%d')
            
            summary.append(f"{date_str}: {signal['indicator']} {signal_type} (强度: {strength})")
        
        return "\n".join(summary)


if __name__ == "__main__":
    # 测试代码
    analyzer = DivergenceAnalyzer()
    result = analyzer.analyze_fund("501312.SH")
    
    if result:
        print(f"分析完成: {result['ts_code']}")
        print(f"数据点数: {len(result['data'])}")
        print(f"背离信号数: {len(result['divergences'])}")
        
        for div in result['divergences'][:3]:
            print(f"- {div['type']} {div['indicator']} 背离 (强度: {div['strength']:.2f})")
    else:
        print("分析失败")