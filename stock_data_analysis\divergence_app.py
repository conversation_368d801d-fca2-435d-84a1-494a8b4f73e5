#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经典背离趋势分析工具 - Streamlit界面
专注于背离信号的识别和展示

版本: 3.0 Divergence Focus
启动命令: streamlit run divergence_app.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import traceback
from divergence_analyzer import DivergenceAnalyzer

# 页面配置
st.set_page_config(
    page_title="经典背离趋势分析工具",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 1rem;
    }
    .signal-bullish {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
        padding: 10px;
        margin: 5px 0;
        border-radius: 5px;
    }
    .signal-bearish {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
        padding: 10px;
        margin: 5px 0;
        border-radius: 5px;
    }
</style>
""", unsafe_allow_html=True)

# 初始化分析器
@st.cache_resource
def get_analyzer():
    return DivergenceAnalyzer()

@st.cache_data(ttl=1800)
def analyze_fund_cached(ts_code, days):
    analyzer = get_analyzer()
    return analyzer.analyze_fund(ts_code, days)

def create_divergence_chart(result):
    """创建背离分析图表"""
    if not result:
        return None
    
    dates = result['dates']
    prices = result['prices']
    indicators = result['indicators']
    divergences = result['divergences']
    
    # 创建子图
    fig = make_subplots(
        rows=4, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.03,
        subplot_titles=('价格走势', 'MACD指标', 'RSI指标', 'KDJ指标'),
        row_heights=[0.5, 0.3, 0.1, 0.1]
    )
    
    # 价格线
    fig.add_trace(
        go.Scatter(x=dates, y=prices, name='收盘价', line=dict(color='blue', width=2)),
        row=1, col=1
    )
    
    # MACD指标
    macd_line = indicators['macd_line']
    macd_signal = indicators['macd_signal']
    macd_hist = indicators['macd_hist']
    
    if not np.all(np.isnan(macd_line)):
        fig.add_trace(
            go.Scatter(x=dates, y=macd_line, name='MACD', line=dict(color='blue')),
            row=2, col=1
        )
        fig.add_trace(
            go.Scatter(x=dates, y=macd_signal, name='Signal', line=dict(color='red')),
            row=2, col=1
        )
        fig.add_trace(
            go.Bar(x=dates, y=macd_hist, name='Histogram', marker_color='gray', opacity=0.6),
            row=2, col=1
        )
    
    # RSI指标
    rsi = indicators['rsi']
    if not np.all(np.isnan(rsi)):
        fig.add_trace(
            go.Scatter(x=dates, y=rsi, name='RSI', line=dict(color='purple')),
            row=3, col=1
        )
        # RSI超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
    
    # KDJ指标
    kdj_k = indicators['kdj_k']
    kdj_d = indicators['kdj_d']
    kdj_j = indicators['kdj_j']
    
    if not np.all(np.isnan(kdj_k)):
        fig.add_trace(
            go.Scatter(x=dates, y=kdj_k, name='K', line=dict(color='blue')),
            row=4, col=1
        )
        fig.add_trace(
            go.Scatter(x=dates, y=kdj_d, name='D', line=dict(color='red')),
            row=4, col=1
        )
        fig.add_trace(
            go.Scatter(x=dates, y=kdj_j, name='J', line=dict(color='green')),
            row=4, col=1
        )
        # KDJ超买超卖线
        fig.add_hline(y=80, line_dash="dash", line_color="red", row=4, col=1)
        fig.add_hline(y=20, line_dash="dash", line_color="green", row=4, col=1)
    
    # 添加背离标记
    for div in divergences:
        p1, p2 = div['price_points']
        date1, date2 = dates.iloc[p1], dates.iloc[p2]
        price1, price2 = div['price_values']
        
        # 确定颜色和符号
        if div['type'] == 'bullish':
            color = 'green'
            symbol = 'triangle-up'
            name = f"底背离({div['indicator']})"
        else:
            color = 'red'
            symbol = 'triangle-down'
            name = f"顶背离({div['indicator']})"
        
        # 在价格图上标记背离点
        fig.add_trace(
            go.Scatter(
                x=[date1, date2], y=[price1, price2],
                mode='markers+lines',
                marker=dict(size=12, symbol=symbol, color=color),
                line=dict(color=color, dash='dot'),
                name=name,
                showlegend=True
            ),
            row=1, col=1
        )
    
    # 添加金叉死叉标记
    if 'golden_death_signals' in result:
        for signal in result['golden_death_signals']:
            idx = signal['index']
            date = signal['date']
            
            if signal['indicator'] == 'MACD':
                row_num = 2
                y_value = signal['fast_value']
            elif signal['indicator'] == 'KDJ':
                row_num = 4
                y_value = signal['fast_value']
            else:
                continue
            
            # 确定颜色和符号
            if signal['type'] == 'golden_cross':
                color = 'gold'
                symbol = 'star'
                name = f"{signal['indicator']}金叉"
            else:
                color = 'black'
                symbol = 'x'
                name = f"{signal['indicator']}死叉"
            
            # 在对应指标图上标记金叉死叉点
            fig.add_trace(
                go.Scatter(
                    x=[date], y=[y_value],
                    mode='markers',
                    marker=dict(size=15, symbol=symbol, color=color, line=dict(width=2, color='white')),
                    name=name,
                    showlegend=True
                ),
                row=row_num, col=1
            )
    
    # 更新布局
    fig.update_layout(
        height=800,
        title_text=f"{result['ts_code']} 背离分析图表",
        showlegend=True,
        hovermode='x unified'
    )
    
    # 设置Y轴范围
    fig.update_yaxes(range=[0, 100], row=3, col=1)  # RSI
    fig.update_yaxes(range=[0, 100], row=4, col=1)  # KDJ
    
    return fig

def display_golden_death_signals(signals):
    """显示金叉死叉信号列表"""
    if not signals:
        st.info("🔍 未检测到金叉死叉信号")
        return
    
    st.markdown("### ⚡ 金叉死叉信号")
    
    for i, signal in enumerate(signals[:10]):  # 最多显示10个信号
        # 信号类型和颜色
        if signal['type'] == 'golden_cross':
            signal_type = "金叉 ⭐"
            css_class = "signal-bullish"
            emoji = "📈"
        else:
            signal_type = "死叉 ❌"
            css_class = "signal-bearish"
            emoji = "📉"
        
        # 日期和数值信息
        date_str = signal['date'].strftime('%Y-%m-%d')
        fast_val = signal['fast_value']
        slow_val = signal['slow_value']
        
        # 显示信号
        st.markdown(f"""
        <div class="{css_class}">
            <strong>{emoji} {signal['indicator']} {signal_type}</strong><br>
            📅 日期: {date_str}<br>
            📊 快线值: {fast_val:.4f}<br>
            📊 慢线值: {slow_val:.4f}<br>
            📝 描述: {signal['description']}
        </div>
        """, unsafe_allow_html=True)

def display_divergence_signals(divergences):
    """显示背离信号列表"""
    if not divergences:
        st.info("🔍 未检测到背离信号")
        return
    
    st.markdown("### 🎯 检测到的背离信号")
    
    for i, div in enumerate(divergences[:10]):  # 最多显示10个信号
        # 信号类型和强度
        signal_type = "底背离 📈" if div['type'] == 'bullish' else "顶背离 📉"
        strength_text = "强" if div['strength'] > 1.5 else "中" if div['strength'] > 1.0 else "弱"
        
        # 日期和价格信息
        date_str = div['date2'].strftime('%Y-%m-%d')
        price_change = ((div['price_values'][1] - div['price_values'][0]) / div['price_values'][0]) * 100
        
        # 选择样式
        css_class = "signal-bullish" if div['type'] == 'bullish' else "signal-bearish"
        
        # 显示信号
        st.markdown(f"""
        <div class="{css_class}">
            <strong>{signal_type} - {div['indicator']}指标</strong><br>
            📅 日期: {date_str}<br>
            💪 强度: {strength_text} ({div['strength']:.2f})<br>
            📊 价格变化: {price_change:+.2f}%<br>
            💰 价格区间: {div['price_values'][0]:.3f} → {div['price_values'][1]:.3f}
        </div>
        """, unsafe_allow_html=True)

# display_summary_metrics函数已移除 - 不再需要显示摘要指标

# 主界面
st.markdown("<h1 class='main-header'>📈 经典背离趋势分析工具</h1>", unsafe_allow_html=True)

# 侧边栏
with st.sidebar:
    st.header("🔧 参数设置")
    
    # 获取基金列表
    analyzer = get_analyzer()
    fund_list = analyzer.data_manager.get_fund_list()
    
    # 基金选择
    fund_options = [f"{code}: {name}" for code, name in fund_list.items()]
    selected_fund = st.selectbox("选择基金", ["请选择..."] + fund_options)
    
    # 手动输入选项
    manual_code = st.text_input("或手动输入代码", placeholder="如: 159519.SZ")
    
    # 确定分析的基金代码
    ts_code = None
    if manual_code:
        ts_code = manual_code.strip().upper()
        if not ts_code.endswith(('.SH', '.SZ')):
            if ts_code.startswith(('5', '6', '7')):
                ts_code += '.SH'
            else:
                ts_code += '.SZ'
    elif selected_fund != "请选择...":
        ts_code = selected_fund.split(':')[0].strip()
    
    # 分析参数
    st.markdown("### 📊 分析参数")
    analysis_days = st.selectbox("分析天数", [100, 200, 300, 500], index=1)
    
    # 背离检测参数
    st.markdown("### 🔍 检测参数")
    lookback_period = st.slider("极值点回望期", 10, 30, 20)
    min_distance = st.slider("极值点最小距离", 5, 20, 10)
    
    # 运行分析按钮
    run_analysis = st.button("🚀 开始分析", type="primary", use_container_width=True)
    
    # 工具按钮
    st.markdown("### 🛠️ 工具")
    if st.button("🗑️ 清除缓存"):
        st.cache_data.clear()
        st.success("缓存已清除!")
        st.rerun()

# 主内容区
if run_analysis and ts_code:
    try:
        with st.spinner(f"正在分析 {ts_code}..."):
            # 更新检测器参数
            analyzer.detector.lookback = lookback_period
            analyzer.detector.min_distance = min_distance
            
            # 执行分析
            result = analyze_fund_cached(ts_code, analysis_days)
            
            if result:
                fund_name = fund_list.get(ts_code, "未知基金")
                st.success(f"✅ 分析完成: {ts_code} - {fund_name}")
                
# 显示摘要指标功能已移除
                
                # 创建标签页
                tab1, tab2, tab3, tab4 = st.tabs(["📊 图表分析", "🎯 背离信号", "⚡ 金叉死叉", "📋 数据详情"])
                
                with tab1:
                    st.markdown("### 📈 背离分析图表")
                    chart = create_divergence_chart(result)
                    if chart:
                        st.plotly_chart(chart, use_container_width=True)
                    else:
                        st.error("图表生成失败")
                
                with tab2:
                    display_divergence_signals(result['divergences'])
                    
                    # 最新信号摘要
                    if result['divergences']:
                        st.markdown("### 📝 信号摘要")
                        summary = analyzer.generate_summary(ts_code)
                        st.info(summary)
                
                with tab3:
                    display_golden_death_signals(result.get('golden_death_signals', []))
                    
                    # 金叉死叉信号统计
                    signals = result.get('golden_death_signals', [])
                    if signals:
                        st.markdown("### 📊 信号统计")
                        col1, col2, col3, col4 = st.columns(4)
                        
                        total_signals = len(signals)
                        golden_count = len([s for s in signals if s['type'] == 'golden_cross'])
                        death_count = len([s for s in signals if s['type'] == 'death_cross'])
                        macd_count = len([s for s in signals if s['indicator'] == 'MACD'])
                        kdj_count = len([s for s in signals if s['indicator'] == 'KDJ'])
                        
                        with col1:
                            st.metric("总信号数", total_signals)
                        with col2:
                            st.metric("金叉信号", golden_count)
                        with col3:
                            st.metric("死叉信号", death_count)
                        with col4:
                            latest_signal = signals[0] if signals else None
                            if latest_signal:
                                st.metric("最新信号", latest_signal['description'])
                
                with tab4:
                    st.markdown("### 📊 原始数据")
                    
                    # 数据统计
                    df = result['data']
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("数据点数", len(df))
                    with col2:
                        st.metric("日期范围", f"{df['trade_date'].min()} ~ {df['trade_date'].max()}")
                    with col3:
                        current_price = df['close'].iloc[-1]
                        price_change = ((current_price - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100
                        st.metric("当前价格", f"{current_price:.3f}", f"{price_change:+.2f}%")
                    
                    # 显示数据表
                    st.dataframe(df.tail(50), use_container_width=True)
                    
                    # 下载数据
                    csv = df.to_csv(index=False)
                    st.download_button(
                        "📥 下载数据",
                        csv,
                        f"{ts_code}_data_{datetime.now().strftime('%Y%m%d')}.csv",
                        "text/csv"
                    )
            else:
                st.error(f"❌ 无法获取 {ts_code} 的数据，请检查代码是否正确")
                
    except Exception as e:
        st.error(f"❌ 分析过程中发生错误: {str(e)}")
        with st.expander("错误详情"):
            st.code(traceback.format_exc())

elif not ts_code and run_analysis:
    st.warning("⚠️ 请先选择或输入基金代码")

else:
    # 欢迎页面
    st.markdown("""
    ## 👋 欢迎使用经典背离趋势分析工具
    
    ### 🎯 核心功能：
    - **📊 多指标背离检测**: 基于RSI、MACD、KDJ指标的经典背离分析
    - **⚡ 金叉死叉信号**: 自动识别MACD和KDJ的金叉死叉交叉点
    - **🔍 智能信号识别**: 自动识别底背离（看涨）和顶背离（看跌）信号
    - **📈 可视化分析**: 直观的图表展示价格走势、背离点和交叉信号
    - **⚡ 实时分析**: 快速获取最新的背离信号和金叉死叉趋势判断
    
    ### 📋 背离原理：
    - **底背离**: 价格创新低，但技术指标未创新低，通常预示反弹机会
    - **顶背离**: 价格创新高，但技术指标未创新高，通常预示回调风险
    
    ### ⚡ 金叉死叉原理：
    - **金叉**: 快线从下方向上穿越慢线，通常为买入信号
    - **死叉**: 快线从上方向下穿越慢线，通常为卖出信号
    - **MACD金叉死叉**: MACD线与Signal线的交叉
    - **KDJ金叉死叉**: K线与D线的交叉
    
    ### 🚀 使用步骤：
    1. 在左侧选择基金代码或手动输入
    2. 调整分析参数（可选）
    3. 点击"开始分析"按钮
    4. 查看图表和背离信号
    
    ### 💡 使用建议：
    - 建议结合多个指标的背离信号进行判断
    - 关注背离信号的强度，强度越高越可靠
    - 背离信号是参考工具，需结合其他分析方法
    """)
    
    # 显示可用基金
    st.markdown("### 📋 可用基金列表")
    funds_df = pd.DataFrame([
        {"代码": code, "名称": name} 
        for code, name in fund_list.items()
    ])
    st.dataframe(funds_df, use_container_width=True, hide_index=True)

# 页脚
st.markdown("---")
st.markdown(
    "<div style='text-align: center; color: #666;'>"
    "经典背离趋势分析工具 © 2025 | 版本: 3.0 Divergence Focus"
    "</div>", 
    unsafe_allow_html=True
)