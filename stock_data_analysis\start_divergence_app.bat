@echo off
cd /d %~dp0

echo Starting divergence analysis application...
echo Current directory: %CD%
echo Checking Python version...
python --version

echo Checking divergence_app.py file...
if not exist "divergence_app.py" (
    echo Error: divergence_app.py file not found!
    pause
    exit /b 1
)

echo Checking Streamlit installation...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo Error: Streamlit not installed or cannot be imported!
    echo Please run: pip install streamlit
    pause
    exit /b 1
)

echo Starting Streamlit application...
start /b "" pythonw -m streamlit run divergence_app.py --server.headless=true --server.port=8501 > streamlit.log 2>&1

timeout /t 3 >nul
echo Divergence analysis application started in background
echo Visit http://localhost:8501 to view the application
echo If unable to access, check streamlit.log file for error information
echo To stop the application, run stop_divergence_app.bat
pause