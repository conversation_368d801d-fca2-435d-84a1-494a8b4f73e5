@echo off
echo Stopping divergence analysis application...

REM Stop processes using port 8501
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8501 "') do (
    echo Stopping process ID: %%a
    taskkill /F /PID %%a >nul 2>&1
)

REM Stop all processes containing divergence_app.py
for /f "tokens=1" %%a in ('wmic process where "CommandLine like '%%divergence_app.py%%'" get ProcessId /value ^| findstr "="') do (
    for /f "tokens=2 delims==" %%b in ("%%a") do (
        if not "%%b"=="" (
            echo Stopping Divergence application process: %%b
            taskkill /F /PID %%b >nul 2>&1
        )
    )
)

REM Additional safety: stop all streamlit-related pythonw processes
taskkill /F /IM pythonw.exe /FI "WINDOWTITLE eq streamlit*" >nul 2>&1

echo Divergence analysis application stopped
echo Press any key to continue...
pause >nul